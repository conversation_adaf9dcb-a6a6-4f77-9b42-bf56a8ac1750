package metrics

import (
	"sync"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

var (
	// globalAgentMetrics 全局Agent指标收集器实例
	globalAgentMetrics *AgentMetrics
	once               sync.Once
)

// GetGlobalAgentMetrics 获取全局Agent指标收集器实例（单例模式）
func GetGlobalAgentMetrics() *AgentMetrics {
	once.Do(func() {
		globalAgentMetrics = NewAgentMetrics()
		logger.Info("Global agent metrics collector initialized", "Operation", "GetGlobalAgentMetrics")
	})
	return globalAgentMetrics
}

// StartMetricsReporting 启动定期指标报告
func StartMetricsReporting(interval time.Duration) {
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()
		
		logger.Info("Started metrics reporting", "Interval", interval.String(), "Operation", "StartMetricsReporting")
		
		for {
			select {
			case <-ticker.C:
				metrics := GetGlobalAgentMetrics()
				metrics.LogMetricsSummary()
			}
		}
	}()
}

// RecordAgentConnectionWait 记录Agent连接等待指标（便捷函数）
func RecordAgentConnectionWait(agentID string, duration time.Duration, success bool) {
	GetGlobalAgentMetrics().RecordConnectionWait(agentID, duration, success)
}

// RecordAgentCommandSend 记录Agent命令发送指标（便捷函数）
func RecordAgentCommandSend(agentID string, commandType string, duration time.Duration, attempts int, success bool) {
	GetGlobalAgentMetrics().RecordCommandSend(agentID, commandType, duration, attempts, success)
}

// RecordAgentError 记录Agent错误指标（便捷函数）
func RecordAgentError(agentID string, errorType string, errorMessage string) {
	GetGlobalAgentMetrics().RecordError(agentID, errorType, errorMessage)
}

// GetAgentConnectionWaitStats 获取Agent连接等待统计信息（便捷函数）
func GetAgentConnectionWaitStats() ConnectionWaitStats {
	return GetGlobalAgentMetrics().GetConnectionWaitStats()
}

// GetAgentCommandSendStats 获取Agent命令发送统计信息（便捷函数）
func GetAgentCommandSendStats() CommandSendStats {
	return GetGlobalAgentMetrics().GetCommandSendStats()
}

// GetAgentErrorStats 获取Agent错误统计信息（便捷函数）
func GetAgentErrorStats() ErrorStats {
	return GetGlobalAgentMetrics().GetErrorStats()
}
