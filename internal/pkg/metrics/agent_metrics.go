package metrics

import (
	"sync"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// AgentMetrics 用于收集和记录Agent相关的监控指标
type AgentMetrics struct {
	mu sync.RWMutex
	
	// 连接等待指标
	connectionWaitTimes    []time.Duration
	connectionWaitTimeouts int64
	connectionWaitSuccess  int64
	
	// 命令发送指标
	commandSendAttempts   int64
	commandSendSuccesses  int64
	commandSendFailures   int64
	commandSendTimes      []time.Duration
	
	// 重试机制指标
	retryAttempts         map[string]int64 // key: agentID, value: total attempts
	retrySuccessAfterFail int64
	retryFinalFailures    int64
	
	// 错误分类指标
	timeoutErrors      int64
	connectionErrors   int64
	agentNotFoundErrors int64
	otherErrors        int64
}

// NewAgentMetrics 创建新的AgentMetrics实例
func NewAgentMetrics() *AgentMetrics {
	return &AgentMetrics{
		connectionWaitTimes: make([]time.Duration, 0),
		commandSendTimes:    make([]time.Duration, 0),
		retryAttempts:       make(map[string]int64),
	}
}

// RecordConnectionWait 记录连接等待指标
func (m *AgentMetrics) RecordConnectionWait(agentID string, duration time.Duration, success bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if success {
		m.connectionWaitSuccess++
		m.connectionWaitTimes = append(m.connectionWaitTimes, duration)
		logger.Info("Connection wait metrics recorded", "AgentID", agentID, "Duration", duration.String(), "Success", true, "Operation", "RecordConnectionWait")
	} else {
		m.connectionWaitTimeouts++
		logger.Info("Connection wait timeout recorded", "AgentID", agentID, "Duration", duration.String(), "Success", false, "Operation", "RecordConnectionWait")
	}
}

// RecordCommandSend 记录命令发送指标
func (m *AgentMetrics) RecordCommandSend(agentID string, commandType string, duration time.Duration, attempts int, success bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.commandSendAttempts += int64(attempts)
	m.commandSendTimes = append(m.commandSendTimes, duration)
	
	if success {
		m.commandSendSuccesses++
		if attempts > 1 {
			m.retrySuccessAfterFail++
		}
		logger.Info("Command send success recorded", "AgentID", agentID, "CommandType", commandType, "Duration", duration.String(), "Attempts", attempts, "Operation", "RecordCommandSend")
	} else {
		m.commandSendFailures++
		m.retryFinalFailures++
		logger.Info("Command send failure recorded", "AgentID", agentID, "CommandType", commandType, "Duration", duration.String(), "Attempts", attempts, "Operation", "RecordCommandSend")
	}
	
	// 记录重试次数
	m.retryAttempts[agentID] += int64(attempts)
}

// RecordError 记录错误分类指标
func (m *AgentMetrics) RecordError(agentID string, errorType string, errorMessage string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	switch errorType {
	case "timeout":
		m.timeoutErrors++
	case "connection":
		m.connectionErrors++
	case "agent_not_found":
		m.agentNotFoundErrors++
	default:
		m.otherErrors++
	}
	
	logger.Info("Error metrics recorded", "AgentID", agentID, "ErrorType", errorType, "ErrorMessage", errorMessage, "Operation", "RecordError")
}

// GetConnectionWaitStats 获取连接等待统计信息
func (m *AgentMetrics) GetConnectionWaitStats() ConnectionWaitStats {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	stats := ConnectionWaitStats{
		TotalAttempts: m.connectionWaitSuccess + m.connectionWaitTimeouts,
		SuccessCount:  m.connectionWaitSuccess,
		TimeoutCount:  m.connectionWaitTimeouts,
	}
	
	if len(m.connectionWaitTimes) > 0 {
		var total time.Duration
		min := m.connectionWaitTimes[0]
		max := m.connectionWaitTimes[0]
		
		for _, duration := range m.connectionWaitTimes {
			total += duration
			if duration < min {
				min = duration
			}
			if duration > max {
				max = duration
			}
		}
		
		stats.AverageWaitTime = total / time.Duration(len(m.connectionWaitTimes))
		stats.MinWaitTime = min
		stats.MaxWaitTime = max
	}
	
	if stats.TotalAttempts > 0 {
		stats.SuccessRate = float64(stats.SuccessCount) / float64(stats.TotalAttempts) * 100
	}
	
	return stats
}

// GetCommandSendStats 获取命令发送统计信息
func (m *AgentMetrics) GetCommandSendStats() CommandSendStats {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	stats := CommandSendStats{
		TotalAttempts:         m.commandSendAttempts,
		SuccessCount:          m.commandSendSuccesses,
		FailureCount:          m.commandSendFailures,
		RetrySuccessCount:     m.retrySuccessAfterFail,
		RetryFinalFailures:    m.retryFinalFailures,
	}
	
	if len(m.commandSendTimes) > 0 {
		var total time.Duration
		min := m.commandSendTimes[0]
		max := m.commandSendTimes[0]
		
		for _, duration := range m.commandSendTimes {
			total += duration
			if duration < min {
				min = duration
			}
			if duration > max {
				max = duration
			}
		}
		
		stats.AverageSendTime = total / time.Duration(len(m.commandSendTimes))
		stats.MinSendTime = min
		stats.MaxSendTime = max
	}
	
	totalCommands := stats.SuccessCount + stats.FailureCount
	if totalCommands > 0 {
		stats.SuccessRate = float64(stats.SuccessCount) / float64(totalCommands) * 100
		stats.FirstAttemptSuccessRate = float64(stats.SuccessCount-stats.RetrySuccessCount) / float64(totalCommands) * 100
	}
	
	return stats
}

// GetErrorStats 获取错误统计信息
func (m *AgentMetrics) GetErrorStats() ErrorStats {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	return ErrorStats{
		TimeoutErrors:       m.timeoutErrors,
		ConnectionErrors:    m.connectionErrors,
		AgentNotFoundErrors: m.agentNotFoundErrors,
		OtherErrors:         m.otherErrors,
	}
}

// LogMetricsSummary 记录指标摘要到日志
func (m *AgentMetrics) LogMetricsSummary() {
	connectionStats := m.GetConnectionWaitStats()
	commandStats := m.GetCommandSendStats()
	errorStats := m.GetErrorStats()
	
	logger.Info("Agent Metrics Summary - Connection Wait", 
		"TotalAttempts", connectionStats.TotalAttempts,
		"SuccessCount", connectionStats.SuccessCount,
		"TimeoutCount", connectionStats.TimeoutCount,
		"SuccessRate", connectionStats.SuccessRate,
		"AverageWaitTime", connectionStats.AverageWaitTime.String(),
		"MinWaitTime", connectionStats.MinWaitTime.String(),
		"MaxWaitTime", connectionStats.MaxWaitTime.String(),
		"Operation", "LogMetricsSummary")
	
	logger.Info("Agent Metrics Summary - Command Send",
		"TotalAttempts", commandStats.TotalAttempts,
		"SuccessCount", commandStats.SuccessCount,
		"FailureCount", commandStats.FailureCount,
		"SuccessRate", commandStats.SuccessRate,
		"FirstAttemptSuccessRate", commandStats.FirstAttemptSuccessRate,
		"RetrySuccessCount", commandStats.RetrySuccessCount,
		"AverageSendTime", commandStats.AverageSendTime.String(),
		"MinSendTime", commandStats.MinSendTime.String(),
		"MaxSendTime", commandStats.MaxSendTime.String(),
		"Operation", "LogMetricsSummary")
	
	logger.Info("Agent Metrics Summary - Errors",
		"TimeoutErrors", errorStats.TimeoutErrors,
		"ConnectionErrors", errorStats.ConnectionErrors,
		"AgentNotFoundErrors", errorStats.AgentNotFoundErrors,
		"OtherErrors", errorStats.OtherErrors,
		"Operation", "LogMetricsSummary")
}

// ConnectionWaitStats 连接等待统计信息
type ConnectionWaitStats struct {
	TotalAttempts   int64         `json:"total_attempts"`
	SuccessCount    int64         `json:"success_count"`
	TimeoutCount    int64         `json:"timeout_count"`
	SuccessRate     float64       `json:"success_rate"`
	AverageWaitTime time.Duration `json:"average_wait_time"`
	MinWaitTime     time.Duration `json:"min_wait_time"`
	MaxWaitTime     time.Duration `json:"max_wait_time"`
}

// CommandSendStats 命令发送统计信息
type CommandSendStats struct {
	TotalAttempts             int64         `json:"total_attempts"`
	SuccessCount              int64         `json:"success_count"`
	FailureCount              int64         `json:"failure_count"`
	SuccessRate               float64       `json:"success_rate"`
	FirstAttemptSuccessRate   float64       `json:"first_attempt_success_rate"`
	RetrySuccessCount         int64         `json:"retry_success_count"`
	RetryFinalFailures        int64         `json:"retry_final_failures"`
	AverageSendTime           time.Duration `json:"average_send_time"`
	MinSendTime               time.Duration `json:"min_send_time"`
	MaxSendTime               time.Duration `json:"max_send_time"`
}

// ErrorStats 错误统计信息
type ErrorStats struct {
	TimeoutErrors       int64 `json:"timeout_errors"`
	ConnectionErrors    int64 `json:"connection_errors"`
	AgentNotFoundErrors int64 `json:"agent_not_found_errors"`
	OtherErrors         int64 `json:"other_errors"`
}
