package agent_service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/metrics"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/retry"

	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/repository"
)

const (
	// Agent连接检查相关常量
	DefaultConnectionCheckInterval = 2 * time.Second // 连接检查间隔
)

// AgentManagerService 管理 agent 的连接和通信
type AgentManagerService struct {
	repo repository.AgentRepository
}

// NewAgentManagerService 创建一个新的 AgentManagerService
func NewAgentManagerService(repo repository.AgentRepository) *AgentManagerService {
	return &AgentManagerService{
		repo: repo,
	}
}

// RegisterAgent 注册 agent
func (s *AgentManagerService) RegisterAgent(ctx context.Context, agent *entity.Agent) error {
	return s.repo.Register(ctx, agent)
}

// UnregisterAgent 注销 agent
func (s *AgentManagerService) UnregisterAgent(ctx context.Context, agentID string) error {
	return s.repo.Unregister(ctx, agentID)
}

// SendCommandToAgent 向指定 agent 发送命令
func (s *AgentManagerService) SendCommandToAgent(ctx context.Context, agentID string, cmd *entity.Command) error {
	return s.repo.SendCommand(ctx, agentID, cmd)
}

// GetAgent 获取 agent 信息
func (s *AgentManagerService) GetAgent(ctx context.Context, agentID string) (*entity.Agent, error) {
	return s.repo.GetByID(ctx, agentID)
}

// ListAgents 获取所有 agent
func (s *AgentManagerService) ListAgents(ctx context.Context) ([]*entity.Agent, error) {
	return s.repo.ListAgents(ctx)
}

// SendCommandToAgentAndWait 向指定的 agent 发送命令并等待响应
func (s *AgentManagerService) SendCommandToAgentAndWait(ctx context.Context, agentID string, cmd *entity.Command) (*dto.Response, error) {
	rawResp, err := s.repo.SendCommandAndWait(ctx, agentID, cmd)
	if err != nil {
		return nil, fmt.Errorf("failed to send command and wait for agent %s: %w", agentID, err)
	}

	var response dto.Response
	if err := json.Unmarshal(rawResp, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response from agent %s: %w", agentID, err)
	}

	return &response, nil
}

// IsAgentConnected 检查agent是否已连接
func (s *AgentManagerService) IsAgentConnected(ctx context.Context, agentID string) bool {
	agent, err := s.repo.GetByID(ctx, agentID)
	if err != nil {
		logger.Debug("Agent connection check failed", "AgentID", agentID, "Error", err.Error(), "Operation", "IsAgentConnected")
		return false
	}

	connected := agent.Connected
	logger.Debug("Agent connection status checked", "AgentID", agentID, "Connected", connected, "Operation", "IsAgentConnected")
	return connected
}

// WaitForAgentConnection 等待agent连接建立
// 注意：此方法会创建ticker和context，确保在函数结束时正确回收资源
func (s *AgentManagerService) WaitForAgentConnection(ctx context.Context, agentID string, timeout time.Duration) error {
	startTime := time.Now()
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel() // 确保context资源被回收

	ticker := time.NewTicker(DefaultConnectionCheckInterval)
	defer ticker.Stop() // 确保ticker资源被回收

	logger.Info("Starting agent connection wait", "AgentID", agentID, "Timeout", timeout.String(), "Operation", "WaitForAgentConnection")

	checkCount := 0
	for {
		select {
		case <-ctx.Done():
			waitDuration := time.Since(startTime)
			logger.Error("Agent connection wait timeout", "AgentID", agentID, "Timeout", timeout.String(), "ActualWaitTime", waitDuration.String(), "CheckCount", checkCount, "Operation", "WaitForAgentConnection")

			// 记录连接等待失败的指标
			metrics.RecordAgentConnectionWait(agentID, waitDuration, false)
			metrics.RecordAgentError(agentID, "timeout", fmt.Sprintf("timeout waiting for agent %s to connect after %v", agentID, timeout))

			return fmt.Errorf("timeout waiting for agent %s to connect after %v", agentID, timeout)
		case <-ticker.C:
			checkCount++
			if s.IsAgentConnected(ctx, agentID) {
				waitDuration := time.Since(startTime)
				logger.Info("Agent connection established successfully", "AgentID", agentID, "WaitTime", waitDuration.String(), "CheckCount", checkCount, "Operation", "WaitForAgentConnection")

				// 记录连接等待成功的指标
				metrics.RecordAgentConnectionWait(agentID, waitDuration, true)

				return nil
			}
			logger.Debug("Agent connection check in progress", "AgentID", agentID, "CheckCount", checkCount, "ElapsedTime", time.Since(startTime).String(), "Operation", "WaitForAgentConnection")
		}
	}
}

// SendCommandToAgentWithRetry 使用重试机制发送命令
func (s *AgentManagerService) SendCommandToAgentWithRetry(ctx context.Context, agentID string, cmd *entity.Command, retryConfig *retry.RetryConfig) error {
	startTime := time.Now()
	logger.Info("Starting command send with retry", "AgentID", agentID, "CommandType", cmd.Type, "MaxAttempts", retryConfig.MaxAttempts, "Operation", "SendCommandToAgentWithRetry")

	attemptCount := 0
	// 使用DoWithResult包装无返回值的函数
	_, err := retry.DoWithResult(ctx, retryConfig, func() (any, error) {
		attemptCount++
		attemptStartTime := time.Now()

		logger.Debug("Attempting to send command", "AgentID", agentID, "CommandType", cmd.Type, "AttemptNumber", attemptCount, "Operation", "SendCommandToAgentWithRetry")

		err := s.repo.SendCommand(ctx, agentID, cmd)
		attemptDuration := time.Since(attemptStartTime)

		if err != nil {
			logger.Warn("Command send attempt failed", "AgentID", agentID, "CommandType", cmd.Type, "AttemptNumber", attemptCount, "AttemptDuration", attemptDuration.String(), "Error", err.Error(), "Operation", "SendCommandToAgentWithRetry")
		} else {
			logger.Debug("Command send attempt succeeded", "AgentID", agentID, "CommandType", cmd.Type, "AttemptNumber", attemptCount, "AttemptDuration", attemptDuration.String(), "Operation", "SendCommandToAgentWithRetry")
		}

		return nil, err // 返回nil作为结果，只关心错误
	})

	totalDuration := time.Since(startTime)
	if err != nil {
		logger.Error("Command send failed after all retries", "AgentID", agentID, "CommandType", cmd.Type, "TotalAttempts", attemptCount, "TotalDuration", totalDuration.String(), "FinalError", err.Error(), "Operation", "SendCommandToAgentWithRetry")

		// 记录命令发送失败的指标
		metrics.RecordAgentCommandSend(agentID, cmd.Type, totalDuration, attemptCount, false)

		// 根据错误类型记录错误指标
		errorType := "other"
		if contains(err.Error(), "timeout") {
			errorType = "timeout"
		} else if contains(err.Error(), "connection") {
			errorType = "connection"
		} else if contains(err.Error(), "not found") {
			errorType = "agent_not_found"
		}
		metrics.RecordAgentError(agentID, errorType, err.Error())
	} else {
		logger.Info("Command sent successfully", "AgentID", agentID, "CommandType", cmd.Type, "TotalAttempts", attemptCount, "TotalDuration", totalDuration.String(), "Operation", "SendCommandToAgentWithRetry")

		// 记录命令发送成功的指标
		metrics.RecordAgentCommandSend(agentID, cmd.Type, totalDuration, attemptCount, true)
	}

	return err
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
