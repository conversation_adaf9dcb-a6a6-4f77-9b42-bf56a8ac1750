package workflow_service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/go-github/v72/github"
	"github.com/web-builder-dev/be-web-builder/internal/application/agent_service"
	appGitHub "github.com/web-builder-dev/be-web-builder/internal/application/github_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/netlify_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	agentEntity "github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
	"github.com/web-builder-dev/be-web-builder/internal/domain/project/repository"
	infraGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	infraNetlify "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	"github.com/web-builder-dev/be-web-builder/internal/interface/dto"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/retry"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/stringutils"
)

const (
	// Netlify 相关常量
	DefaultNetlifyBuildCmd   = "npm run build" // Example build command
	DefaultNetlifyPublishDir = "dist"          // Example publish directory

	// Agent 连接相关常量
	DefaultAgentConnectionWaitTimeout = 1 * time.Minute // Agent连接等待超时时间

	// 重试机制相关常量
	DefaultRetryMaxAttempts   = 5                // 最大重试次数
	DefaultRetryInitialDelay  = 2 * time.Second  // 初始重试延迟
	DefaultRetryMaxDelay      = 10 * time.Second // 最大重试延迟
	DefaultRetryBackoffFactor = 1.5              // 退避因子
	DefaultRetryJitterFactor  = 0.1              // 抖动因子
)

// WorkflowFileContent 定义了要提交到版本库的文件内容。
type WorkflowFileContent struct {
	Path    string `json:"path"`
	Content string `json:"content"`
}

// CommitAndDeployRequest 封装了 CommitAndDeployWorkflow 的所有参数。
type CommitAndDeployRequest struct {
	ProjectID     string                `json:"project_id"`
	Files         []WorkflowFileContent `json:"files,omitempty"`
	DeletedFiles  []string              `json:"deleted_files,omitempty"`
	CommitMessage string                `json:"commit_message,omitempty"`
	RepoID        string                `json:"repo_id,omitempty"`
	NetlifySiteID string                `json:"netlify_site_id,omitempty"`
}

// CommitAndDeployService 提供了提交代码到 GitHub 并触发 Netlify 构建的工作流服务。
type CommitAndDeployService struct {
	projectRepo     repository.ProjectRepository
	githubInfra     *infraGitHub.Service
	codeCommitSvc   *appGitHub.CodeCommitService
	netlifyInfra    *infraNetlify.Service
	initNetlifySvc  *netlify_service.InitNetlifySiteService
	agentManagerSvc *agent_service.AgentManagerService
	cfg             *config.Config
	statusRepo      repository.ProjectStatusRepository
	screenshotSvc   *ScreenshotService
}

// NewCommitAndDeployService 创建一个新的 CommitAndDeployService 实例。
func NewCommitAndDeployService(
	projectRepo repository.ProjectRepository,
	statusRepo repository.ProjectStatusRepository,
	githubInfra *infraGitHub.Service,
	codeCommitSvc *appGitHub.CodeCommitService,
	netlifyInfra *infraNetlify.Service,
	initNetlifySvc *netlify_service.InitNetlifySiteService,
	agentManagerSvc *agent_service.AgentManagerService,
	cfg *config.Config,
	screenshotSvc *ScreenshotService,
) *CommitAndDeployService {
	return &CommitAndDeployService{
		projectRepo:     projectRepo,
		githubInfra:     githubInfra,
		codeCommitSvc:   codeCommitSvc,
		netlifyInfra:    netlifyInfra,
		initNetlifySvc:  initNetlifySvc,
		agentManagerSvc: agentManagerSvc,
		cfg:             cfg,
		statusRepo:      statusRepo,
		screenshotSvc:   screenshotSvc,
	}
}

// CommitAndDeployWorkflowWithFly 执行提交代码到 GitHub 并通过 Agent 更新部署的工作流
func (s *CommitAndDeployService) CommitAndDeployWorkflowWithFly(ctx context.Context, req *CommitAndDeployRequest) error {
	logger.Info("Workflow Service: Starting commit-and-deploy process with Fly", "ProjectID", req.ProjectID)

	finalRepoName := req.RepoID
	var err error

	// --- 步骤 1: 提交代码到 GitHub ---
	logger.Info("Step 1: Committing code to GitHub", "ProjectID", req.ProjectID)
	finalRepoName, err = s.handleGitHubCommit(ctx, req, finalRepoName)
	if err != nil {
		return fmt.Errorf("GitHub commit step failed: %w", err)
	}
	logger.Info("GitHub commit step completed", "ProjectID", req.ProjectID, "FinalRepoName", finalRepoName)

	// --- 步骤 2: 等待Agent连接并发送 update_code 命令 ---
	step2StartTime := time.Now()
	logger.Info("Step 2: Starting agent connection wait and update_code command", "ProjectID", req.ProjectID, "Operation", "CommitAndDeployWorkflowWithFly_Step2")
	agentID := req.ProjectID // 直接用 ProjectID 作为 AgentID

	// 首先等待agent连接建立
	waitTimeout := DefaultAgentConnectionWaitTimeout
	logger.Info("Initiating agent connection wait", "ProjectID", req.ProjectID, "AgentID", agentID, "WaitTimeout", waitTimeout.String(), "Operation", "CommitAndDeployWorkflowWithFly_AgentWait")

	if err := s.agentManagerSvc.WaitForAgentConnection(ctx, agentID, waitTimeout); err != nil {
		step2Duration := time.Since(step2StartTime)
		logger.Error("Agent connection wait failed", "ProjectID", req.ProjectID, "AgentID", agentID, "WaitTimeout", waitTimeout.String(), "Step2Duration", step2Duration.String(), "Error", err.Error(), "Operation", "CommitAndDeployWorkflowWithFly_AgentWait")
		return fmt.Errorf("failed to wait for agent connection %s: %w", agentID, err)
	}

	agentWaitDuration := time.Since(step2StartTime)
	logger.Info("Agent connection established", "ProjectID", req.ProjectID, "AgentID", agentID, "AgentWaitDuration", agentWaitDuration.String(), "Operation", "CommitAndDeployWorkflowWithFly_AgentWait")

	// 使用重试机制发送命令作为额外保障
	commandStartTime := time.Now()
	retryConfig := &retry.RetryConfig{
		MaxAttempts:   DefaultRetryMaxAttempts,
		InitialDelay:  DefaultRetryInitialDelay,
		MaxDelay:      DefaultRetryMaxDelay,
		BackoffFactor: DefaultRetryBackoffFactor,
		JitterFactor:  DefaultRetryJitterFactor,
		RetryableErrors: []string{
			"agent with id",
			"not found for sending command",
			"connection refused",
			"timeout",
		},
	}

	logger.Info("Starting update_code command send with retry", "ProjectID", req.ProjectID, "AgentID", agentID, "MaxAttempts", retryConfig.MaxAttempts, "Operation", "CommitAndDeployWorkflowWithFly_CommandSend")

	cmd := agentEntity.NewUpdateCodeCommand()
	if err := s.agentManagerSvc.SendCommandToAgentWithRetry(ctx, agentID, &cmd, retryConfig); err != nil {
		commandDuration := time.Since(commandStartTime)
		step2Duration := time.Since(step2StartTime)
		logger.Error("update_code command send failed after all retries", "ProjectID", req.ProjectID, "AgentID", agentID, "CommandDuration", commandDuration.String(), "Step2Duration", step2Duration.String(), "Error", err.Error(), "Operation", "CommitAndDeployWorkflowWithFly_CommandSend")
		return fmt.Errorf("failed to send update_code command to agent %s: %w", agentID, err)
	}

	commandDuration := time.Since(commandStartTime)
	step2Duration := time.Since(step2StartTime)
	logger.Info("update_code command sent successfully", "ProjectID", req.ProjectID, "AgentID", agentID, "CommandDuration", commandDuration.String(), "Step2Duration", step2Duration.String(), "AgentWaitDuration", agentWaitDuration.String(), "Operation", "CommitAndDeployWorkflowWithFly_CommandSend")
	//调用截图服务
	siteURL := fmt.Sprintf("https://%s.fly.dev", req.ProjectID)
	s.screenshotSvc.AsyncUpdateSiteThumbnailByScreenshotWithProjectID(ctx, req.ProjectID, siteURL, s.statusRepo)
	logger.Info("Screenshot service called for site thumbnail update", "ProjectID", req.ProjectID, "SiteURL", siteURL)

	// --- 步骤 3: 更新项目状态记录 ---
	logger.Info("Step 3: Updating Project status record", "ProjectID", req.ProjectID)
	updates := map[string]any{
		"status":     "done",
		"error_code": "",
		"error_msg":  "",
		// "preview_link": fmt.Sprintf("https://%s.fly.dev", req.ProjectID),
		"preview_link": fmt.Sprintf("https://id-preview-%s.webbuilder.world", req.ProjectID),
	}
	_, err = s.statusRepo.UpdateByProjectID(ctx, req.ProjectID, updates)
	if err != nil {
		logger.Error("Failed to update project status record, but workflow continues", "ProjectID", req.ProjectID, "Error", err)
	} else {
		logger.Info("Project status update step completed", "ProjectID", req.ProjectID)
	}

	logger.Info("Workflow Service: commit-and-deploy with Fly process finished successfully", "ProjectID", req.ProjectID)
	return nil
}

// CommitAndDeployWorkflowWithNetlify 执行提交代码到 GitHub 并触发 Netlify 构建的后台工作流。
// 同时更新 Supabase 中对应的 Project 记录。
func (s *CommitAndDeployService) CommitAndDeployWorkflowWithNetlify(ctx context.Context, req *CommitAndDeployRequest) error {
	logger.Info("Workflow Service: Starting commit-and-deploy process", "ProjectID", req.ProjectID)

	finalRepoName := req.RepoID
	finalNetlifySiteID := req.NetlifySiteID
	var err error

	// --- 步骤 1: 提交代码到 GitHub ---
	logger.Info("Step 1: Committing code to GitHub", "ProjectID", req.ProjectID)
	finalRepoName, err = s.handleGitHubCommit(ctx, req, finalRepoName)
	if err != nil {
		return fmt.Errorf("GitHub commit step failed: %w", err)
	}
	logger.Info("GitHub commit step completed", "ProjectID", req.ProjectID, "FinalRepoName", finalRepoName)

	// --- 步骤 2: 部署代码到 Netlify ---
	logger.Info("Step 2: Deploying code to Netlify", "ProjectID", req.ProjectID)
	if finalRepoName == "" { // Should have been caught by handleGitHubCommit if no initial repo_id and no files
		return fmt.Errorf("cannot proceed with Netlify deployment, GitHub repository name is not determined")
	}

	finalNetlifySiteID, err = s.handleNetlifyDeployment(ctx, req, finalRepoName, finalNetlifySiteID)
	if err != nil {
		return fmt.Errorf("netlify deployment step failed: %w", err)
	}
	logger.Info("Netlify deployment step completed", "ProjectID", req.ProjectID, "FinalNetlifySiteID", finalNetlifySiteID)

	// --- 步骤 3: 更新项目记录 ---
	logger.Info("Step 3: Updating Project record", "ProjectID", req.ProjectID)
	err = s.updateProjectRecord(ctx, req.ProjectID, finalRepoName, finalNetlifySiteID, req.RepoID == "", req.NetlifySiteID == "")
	if err != nil {
		// 根据 Python 示例，更新失败不应阻止整个流程，仅记录错误
		logger.Error("Failed to update project record, but workflow continues", "ProjectID", req.ProjectID, "Error", err)
	} else {
		logger.Info("Project record update step completed (if applicable)", "ProjectID", req.ProjectID)
	}

	logger.Info("Workflow Service: commit-and-deploy process finished successfully", "ProjectID", req.ProjectID)
	return nil
}

// handleGitHubCommit 处理 GitHub 相关的操作：创建仓库（如果需要）和提交文件。
// 返回最终的仓库名和错误（如果有）。
func (s *CommitAndDeployService) handleGitHubCommit(ctx context.Context, req *CommitAndDeployRequest, currentRepoName string) (string, error) {
	hasFileChanges := len(req.Files) > 0 || len(req.DeletedFiles) > 0

	if !hasFileChanges && currentRepoName == "" {
		logger.Warn("No files to commit/delete and no existing repository ID provided. Workflow cannot continue.", "ProjectID", req.ProjectID)
		return "", fmt.Errorf("no files to commit/delete and no existing repository ID provided")
	}

	if !hasFileChanges {
		logger.Info("No file changes provided, skipping GitHub commit step. Using existing repository.", "ProjectID", req.ProjectID, "RepoName", currentRepoName)
		return currentRepoName, nil // 返回现有仓库名，不执行提交
	}

	finalRepoName := currentRepoName
	var err error

	if finalRepoName == "" {
		// 创建新仓库
		repoName := fmt.Sprintf("project-%s-%d", stringutils.ShortenUUID(req.ProjectID), time.Now().Unix())
		logger.Info("No existing RepoID, creating new repository", "ProjectID", req.ProjectID, "NewRepoName", repoName)
		createdRepo, _, errGH := s.githubInfra.Repositories.CreateUserRepository(ctx, &github.Repository{
			Name:       github.Ptr(repoName),
			Private:    github.Ptr(true),
			AutoInit:   github.Ptr(true), // Auto-init to allow immediate commit
			IsTemplate: github.Ptr(true),
		})
		if errGH != nil {
			logger.Error("Failed to create new GitHub repository for user", "ProjectID", req.ProjectID, "RepoName", repoName, "Error", errGH)
			return "", fmt.Errorf("creating new GitHub repository %s for user failed: %w", repoName, errGH)
		}
		finalRepoName = createdRepo.GetName()
		logger.Info("New GitHub repository created successfully", "ProjectID", req.ProjectID, "FinalRepoName", finalRepoName)
	}

	commitMsg := req.CommitMessage
	if commitMsg == "" {
		commitMsg = fmt.Sprintf("Update project %s at %s by workflow", req.ProjectID, time.Now().Format("2006-01-02 15:04:05"))
	}

	// 准备 dto.CommitRequest
	commitReqDTOFiles := make([]dto.FileContent, len(req.Files))
	for i, f := range req.Files {
		commitReqDTOFiles[i] = dto.FileContent{
			Path:    f.Path,
			Content: f.Content,
		}
	}

	commitReq := &dto.CommitRequest{
		Owner:         s.cfg.GitHub.Owner,
		Repository:    finalRepoName,
		Branch:        "main",
		CommitMessage: commitMsg,
		Files:         commitReqDTOFiles,
		DeletedFiles:  req.DeletedFiles,
	}

	logger.Info("Calling CodeCommitService.CommitChanges", "ProjectID", req.ProjectID, "RepoName", finalRepoName)
	_, err = s.codeCommitSvc.CommitChanges(ctx, commitReq)
	if err != nil {
		logger.Error("CodeCommitService.CommitChanges failed", "ProjectID", req.ProjectID, "RepoName", finalRepoName, "Error", err)
		return finalRepoName, fmt.Errorf("committing changes via CodeCommitService to repository %s failed: %w", finalRepoName, err)
	}

	logger.Info("Changes committed to GitHub successfully via CodeCommitService", "ProjectID", req.ProjectID, "RepoName", finalRepoName)
	return finalRepoName, nil
}

// handleNetlifyDeployment 处理 Netlify 相关的操作：创建站点（如果需要）和触发构建。
// 返回最终的 Netlify Site ID 和错误（如果有）。
func (s *CommitAndDeployService) handleNetlifyDeployment(ctx context.Context, req *CommitAndDeployRequest, repoName, currentNetlifySiteID string) (string, error) {
	finalNetlifySiteID := currentNetlifySiteID
	var err error
	//检查一下这个项目有没有NetlifySiteID，防止req里没有NetlifySiteID但是项目后来更新了NetlifySiteID
	p, pError := s.projectRepo.GetByID(ctx, req.ProjectID)
	if pError != nil {
		logger.Error("Failed to get project for update", "ProjectID", req.ProjectID, "Error", pError)
		return finalNetlifySiteID, fmt.Errorf("failed to get project %s for update: %w", req.ProjectID, pError)
	}
	finalNetlifySiteID = p.NetlifySiteID

	if finalNetlifySiteID != "" {
		logger.Info("Existing NetlifySiteID provided, triggering build for site", "ProjectID", req.ProjectID, "NetlifySiteID", finalNetlifySiteID)
		build, errBuild := s.netlifyInfra.Builds.CreateSiteBuild(ctx, finalNetlifySiteID)
		if errBuild != nil {
			logger.Error("Failed to trigger Netlify site build", "ProjectID", req.ProjectID, "NetlifySiteID", finalNetlifySiteID, "Error", errBuild)
			return finalNetlifySiteID, fmt.Errorf("triggering Netlify build for site %s failed: %w", finalNetlifySiteID, errBuild)
		}
		logger.Info("Netlify site build triggered successfully", "ProjectID", req.ProjectID, "NetlifySiteID", finalNetlifySiteID, "BuildID", build.ID, "DeployID", build.DeployID)
	} else {
		logger.Info("No existing NetlifySiteID, creating and configuring new Netlify site via InitNetlifySiteCore.", "ProjectID", req.ProjectID, "RepoName", repoName)
		if s.initNetlifySvc == nil {
			logger.Error("InitNetlifySiteService is not initialized in CommitAndDeployService. Cannot create new Netlify site.", "ProjectID", req.ProjectID)
			return "", fmt.Errorf("InitNetlifySiteService is not available")
		}

		githubOwner := s.cfg.GitHub.Owner
		finalNetlifySiteID, err = s.initNetlifySvc.InitNetlifySiteCore(ctx, repoName, githubOwner, DefaultNetlifyBuildCmd, DefaultNetlifyPublishDir)
		if err != nil {
			logger.Error("Failed to create and configure Netlify site via InitNetlifySiteCore", "ProjectID", req.ProjectID, "RepoName", repoName, "Error", err)
			return "", fmt.Errorf("creating and configuring Netlify site for repo %s failed: %w", repoName, err)
		}
		logger.Info("Netlify site created and configured successfully via InitNetlifySiteCore", "ProjectID", req.ProjectID, "NetlifySiteID", finalNetlifySiteID, "RepoName", repoName)
	}
	return finalNetlifySiteID, nil
}

// updateProjectRecord 更新项目记录中的 repo_id 和/或 netlify_site_id。
// onlyUpdateIfNewRepo/NetlifySite 控制是否仅在新创建时更新。
func (s *CommitAndDeployService) updateProjectRecord(ctx context.Context, projectID, finalRepoName, finalNetlifySiteID string, isNewRepo, isNewNetlifySite bool) error {
	proj, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		logger.Error("Failed to get project for update", "ProjectID", projectID, "Error", err)
		return fmt.Errorf("failed to get project %s for update: %w", projectID, err)
	}

	changed := false
	if finalRepoName != "" && proj.RepoID != finalRepoName {
		proj.RepoID = finalRepoName
		changed = true
		logger.Info("Project RepoID will be updated", "ProjectID", projectID, "OldRepoID", proj.RepoID, "NewRepoID", finalRepoName, "IsNewRepoFlag", isNewRepo)
	}
	if finalNetlifySiteID != "" && proj.NetlifySiteID != finalNetlifySiteID {
		proj.NetlifySiteID = finalNetlifySiteID
		changed = true
		logger.Info("Project NetlifySiteID will be updated", "ProjectID", projectID, "OldNetlifySiteID", proj.NetlifySiteID, "NewNetlifySiteID", finalNetlifySiteID, "IsNewNetlifySiteFlag", isNewNetlifySite)
	}

	if !changed {
		logger.Info("No new attributes or attributes already up-to-date for project.", "ProjectID", projectID)
		return nil
	}
	proj.UpdatedAt = time.Now()

	logger.Info("Attempting to update project record in DB", "ProjectID", projectID, "NewRepoID", proj.RepoID, "NewNetlifySiteID", proj.NetlifySiteID)
	err = s.projectRepo.Update(ctx, proj)
	if err != nil {
		logger.Error("Failed to update project record in DB", "ProjectID", projectID, "Error", err)
		return fmt.Errorf("updating project %s in DB failed: %w", projectID, err)
	}

	logger.Info("Project record updated successfully in DB", "ProjectID", projectID)
	return nil
}
