package fly

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
)

// MachineService 提供了操作 Fly.io Machines 的相关方法
// 参考 AppService 设计
type MachineService struct {
	common service // 嵌入 common service，它包含了 client 的引用
}

// newMachineService 创建一个新的 MachineService 实例
func newMachineService(client *Client) *MachineService {
	return &MachineService{common: service{client: client}}
}

// FlyMachineServiceCheck 表示服务健康检查配置
// 参考 model_fly_machine_service_check.go
type FlyMachineServiceCheck struct {
	GracePeriod   *FlyDuration           `json:"grace_period,omitempty"`
	Headers       []FlyMachineHTTPHeader `json:"headers,omitempty"`
	Interval      *FlyDuration           `json:"interval,omitempty"`
	Method        *string                `json:"method,omitempty"`
	Path          *string                `json:"path,omitempty"`
	Port          *int32                 `json:"port,omitempty"`
	Protocol      *string                `json:"protocol,omitempty"`
	Timeout       *FlyDuration           `json:"timeout,omitempty"`
	TlsServerName *string                `json:"tls_server_name,omitempty"`
	TlsSkipVerify *bool                  `json:"tls_skip_verify,omitempty"`
	Type          *string                `json:"type,omitempty"`
}

// FlyMachineServiceConcurrency 表示服务并发配置
// 参考 model_fly_machine_service_concurrency.go
type FlyMachineServiceConcurrency struct {
	HardLimit *int32  `json:"hard_limit,omitempty"`
	SoftLimit *int32  `json:"soft_limit,omitempty"`
	Type      *string `json:"type,omitempty"`
}

// FlyMachinePort 表示服务端口配置
// 参考 model_fly_machine_port.go
type FlyMachinePort struct {
	EndPort           *int32                `json:"end_port,omitempty"`
	ForceHttps        *bool                 `json:"force_https,omitempty"`
	Handlers          []string              `json:"handlers,omitempty"`
	HttpOptions       *FlyHTTPOptions       `json:"http_options,omitempty"`
	Port              *int32                `json:"port,omitempty"`
	ProxyProtoOptions *FlyProxyProtoOptions `json:"proxy_proto_options,omitempty"`
	StartPort         *int32                `json:"start_port,omitempty"`
	TlsOptions        *FlyTLSOptions        `json:"tls_options,omitempty"`
}

// FlyHTTPOptions 表示 HTTP 选项配置
// 参考 model_fly_http_options.go
type FlyHTTPOptions struct {
	Compress           *bool                   `json:"compress,omitempty"`
	H2Backend          *bool                   `json:"h2_backend,omitempty"`
	HeadersReadTimeout *int32                  `json:"headers_read_timeout,omitempty"`
	IdleTimeout        *int32                  `json:"idle_timeout,omitempty"`
	Response           *FlyHTTPResponseOptions `json:"response,omitempty"`
}

// FlyProxyProtoOptions 表示代理协议选项
// 参考 model_fly_proxy_proto_options.go
type FlyProxyProtoOptions struct {
	Version *string `json:"version,omitempty"`
}

// FlyTLSOptions 表示 TLS 选项配置
// 参考 model_fly_tls_options.go
type FlyTLSOptions struct {
	Alpn              []string `json:"alpn,omitempty"`
	DefaultSelfSigned *bool    `json:"default_self_signed,omitempty"`
	Versions          []string `json:"versions,omitempty"`
}

// FlyHTTPResponseOptions 表示 HTTP 响应选项配置
// 参考 model_fly_http_response_options.go
type FlyHTTPResponseOptions struct {
	Headers  map[string]map[string]interface{} `json:"headers,omitempty"`
	Pristine *bool                             `json:"pristine,omitempty"`
}

// FlyMachineCheck 表示机器健康检查配置
// 参考 model_fly_machine_check.go
type FlyMachineCheck struct {
	GracePeriod   *FlyDuration           `json:"grace_period,omitempty"`
	Headers       []FlyMachineHTTPHeader `json:"headers,omitempty"`
	Interval      *FlyDuration           `json:"interval,omitempty"`
	Kind          *string                `json:"kind,omitempty"`
	Method        *string                `json:"method,omitempty"`
	Path          *string                `json:"path,omitempty"`
	Port          *int32                 `json:"port,omitempty"`
	Protocol      *string                `json:"protocol,omitempty"`
	Timeout       *FlyDuration           `json:"timeout,omitempty"`
	TlsSkipVerify *bool                  `json:"tls_skip_verify,omitempty"`
	Type          *string                `json:"type,omitempty"`
	Url           *string                `json:"url,omitempty"`
}

// FlyContainerConfig 表示容器配置
// 参考 model_fly_container_config.go
type FlyContainerConfig struct {
	Env   *map[string]string `json:"env,omitempty"`
	Image *string            `json:"image,omitempty"`
}

// FlyDNSConfig 表示 DNS 配置
// 参考 model_fly_dns_config.go
type FlyDNSConfig struct {
	Options []FlyDNSOption `json:"options,omitempty"`
}

type FlyDNSOption struct {
	Name  *string `json:"name,omitempty"`
	Value *string `json:"value,omitempty"`
}

// FlyFile 表示文件映射
// 参考 model_fly_file.go
type FlyFile struct {
	GuestPath *string `json:"guest_path,omitempty"`
	RawValue  *string `json:"raw_value,omitempty"`
}

// FlyMachineInit 表示初始化配置
// 参考 model_fly_machine_init.go
type FlyMachineInit struct {
	Cmd        []string `json:"cmd,omitempty"`
	Entrypoint *string  `json:"entrypoint,omitempty"`
}

// FlyMachineMetrics 表示监控配置
// 参考 model_fly_machine_metrics.go
type FlyMachineMetrics struct {
	Port *int32  `json:"port,omitempty"`
	Path *string `json:"path,omitempty"`
}

// FlyMachineProcess 表示进程配置
// 参考 model_fly_machine_process.go
type FlyMachineProcess struct {
	Cmd []string `json:"cmd,omitempty"`
}

// FlyMachineRestart 表示重启策略
// 参考 model_fly_machine_restart.go
type FlyMachineRestart struct {
	Policy *string `json:"policy,omitempty"`
}

// FlyMachineService 表示服务配置
// 参考 model_fly_machine_service.go
type FlyMachineService struct {
	Autostart *bool `json:"autostart,omitempty"`
	// Accepts a string (new format) or a boolean (old format). For backward compatibility with older clients, the API continues to use booleans for \"off\" and \"stop\" in responses. * \"off\" or false - Do not autostop the Machine. * \"stop\" or true - Automatically stop the Machine. * \"suspend\" - Automatically suspend the Machine, falling back to a full stop if this is not possible.
	Autostop *bool `json:"autostop,omitempty"`
	// An optional list of service checks
	Checks                   []FlyMachineServiceCheck      `json:"checks,omitempty"`
	Concurrency              *FlyMachineServiceConcurrency `json:"concurrency,omitempty"`
	ForceInstanceDescription *string                       `json:"force_instance_description,omitempty"`
	ForceInstanceKey         *string                       `json:"force_instance_key,omitempty"`
	InternalPort             *int32                        `json:"internal_port,omitempty"`
	MinMachinesRunning       *int32                        `json:"min_machines_running,omitempty"`
	Ports                    []FlyMachinePort              `json:"ports,omitempty"`
	Protocol                 *string                       `json:"protocol,omitempty"`
}

// FlyStatic 表示静态资源配置
// 参考 model_fly_static.go
type FlyStatic struct {
	GuestPath *string `json:"guest_path,omitempty"`
	UrlPrefix *string `json:"url_prefix,omitempty"`
}

// FlyStopConfig 表示停止配置
// 参考 model_fly_stop_config.go
type FlyStopConfig struct {
	Signal  *string `json:"signal,omitempty"`
	Timeout *int32  `json:"timeout,omitempty"`
}

// FlyDuration 表示时间段
// 参考 model_fly_duration.go
type FlyDuration struct {
	TimeDuration *int64 `json:"time.Duration,omitempty"`
}

// FlyMachineHTTPHeader 表示 HTTP 头
// 参考 model_fly_machine_http_header.go
type FlyMachineHTTPHeader struct {
	Name   *string  `json:"name,omitempty"`
	Values []string `json:"values,omitempty"`
}

// FlyMachineMount 表示机器挂载点
// 参考 OpenAPI fly.MachineMount schema
type FlyMachineMount struct {
	AddSizeGb              *int32  `json:"add_size_gb,omitempty"`
	Encrypted              *bool   `json:"encrypted,omitempty"`
	ExtendThresholdPercent *int32  `json:"extend_threshold_percent,omitempty"`
	Name                   *string `json:"name,omitempty"`
	Path                   *string `json:"path,omitempty"`
	SizeGb                 *int32  `json:"size_gb,omitempty"`
	SizeGbLimit            *int32  `json:"size_gb_limit,omitempty"`
	Volume                 *string `json:"volume,omitempty"`
}

// CheckStatus 表示健康检查状态，对齐 openapi-generator 生成的结构体
// name、output、status、updated_at 都为 *string 且 omitempty
// 对应 model_check_status.go
// 对应 OpenAPI CheckStatus
// name(string), output(string), status(string), updated_at(string)
type CheckStatus struct {
	Name      *string `json:"name,omitempty"`
	Output    *string `json:"output,omitempty"`
	Status    *string `json:"status,omitempty"`
	UpdatedAt *string `json:"updated_at,omitempty"`
}

// MachineEvent 表示机器事件，对齐 openapi-generator 生成的结构体
// id、source、status、type 为 *string，timestamp 为 *int64，request 为 map[string]interface{}
type MachineEvent struct {
	Id        *string                `json:"id,omitempty"`
	Request   map[string]interface{} `json:"request,omitempty"`
	Source    *string                `json:"source,omitempty"`
	Status    *string                `json:"status,omitempty"`
	Timestamp *int64                 `json:"timestamp,omitempty"`
	Type      *string                `json:"type,omitempty"`
}

// ImageRef 表示镜像引用，对齐 openapi-generator 生成的结构体
type ImageRef struct {
	Digest     *string            `json:"digest,omitempty"`
	Labels     *map[string]string `json:"labels,omitempty"`
	Registry   *string            `json:"registry,omitempty"`
	Repository *string            `json:"repository,omitempty"`
	Tag        *string            `json:"tag,omitempty"`
}

// FlyMachineGuest 表示 guest 配置，对齐 openapi-generator 生成的结构体
type FlyMachineGuest struct {
	CpuKind          *string  `json:"cpu_kind,omitempty"`
	Cpus             *int32   `json:"cpus,omitempty"`
	GpuKind          *string  `json:"gpu_kind,omitempty"`
	Gpus             *int32   `json:"gpus,omitempty"`
	HostDedicationId *string  `json:"host_dedication_id,omitempty"`
	KernelArgs       []string `json:"kernel_args,omitempty"`
	MemoryMb         *int32   `json:"memory_mb,omitempty"`
}

// FlyMachineConfig 对齐 openapi-generator 生成的结构体
// 只列出部分常用字段，后续可继续补全
type FlyMachineConfig struct {
	AutoDestroy             *bool                       `json:"auto_destroy,omitempty"`
	Checks                  *map[string]FlyMachineCheck `json:"checks,omitempty"`
	Containers              []FlyContainerConfig        `json:"containers,omitempty"`
	DisableMachineAutostart *bool                       `json:"disable_machine_autostart,omitempty"`
	Dns                     *FlyDNSConfig               `json:"dns,omitempty"`
	Env                     *map[string]string          `json:"env,omitempty"`
	Files                   []FlyFile                   `json:"files,omitempty"`
	Guest                   *FlyMachineGuest            `json:"guest,omitempty"`
	Image                   *string                     `json:"image,omitempty"`
	Init                    *FlyMachineInit             `json:"init,omitempty"`
	Metadata                *map[string]string          `json:"metadata,omitempty"`
	Mounts                  []FlyMachineMount           `json:"mounts,omitempty"`
	Metrics                 *FlyMachineMetrics          `json:"metrics,omitempty"`
	Processes               []FlyMachineProcess         `json:"processes,omitempty"`
	Restart                 *FlyMachineRestart          `json:"restart,omitempty"`
	Schedule                *string                     `json:"schedule,omitempty"`
	Services                []FlyMachineService         `json:"services,omitempty"`
	Size                    *string                     `json:"size,omitempty"`
	Standbys                []string                    `json:"standbys,omitempty"`
	Statics                 []FlyStatic                 `json:"statics,omitempty"`
	StopConfig              *FlyStopConfig              `json:"stop_config,omitempty"`
}

// Machine 对齐 openapi-generator 生成的结构体
type Machine struct {
	Checks           []CheckStatus     `json:"checks,omitempty"`
	Config           *FlyMachineConfig `json:"config,omitempty"`
	CreatedAt        *string           `json:"created_at,omitempty"`
	Events           []MachineEvent    `json:"events,omitempty"`
	HostStatus       *string           `json:"host_status,omitempty"`
	Id               *string           `json:"id,omitempty"`
	ImageRef         *ImageRef         `json:"image_ref,omitempty"`
	IncompleteConfig *FlyMachineConfig `json:"incomplete_config,omitempty"`
	InstanceId       *string           `json:"instance_id,omitempty"`
	Name             *string           `json:"name,omitempty"`
	Nonce            *string           `json:"nonce,omitempty"`
	PrivateIp        *string           `json:"private_ip,omitempty"`
	Region           *string           `json:"region,omitempty"`
	State            *string           `json:"state,omitempty"`
	UpdatedAt        *string           `json:"updated_at,omitempty"`
}

// CreateMachineRequest 对齐 openapi-generator 生成的结构体
type CreateMachineRequest struct {
	Config                  *FlyMachineConfig `json:"config,omitempty"`
	LeaseTtl                *int32            `json:"lease_ttl,omitempty"`
	Lsvd                    *bool             `json:"lsvd,omitempty"`
	MinSecretsVersion       *int32            `json:"min_secrets_version,omitempty"`
	Name                    *string           `json:"name,omitempty"`
	Region                  *string           `json:"region,omitempty"`
	SkipLaunch              *bool             `json:"skip_launch,omitempty"`
	SkipSecrets             *bool             `json:"skip_secrets,omitempty"`
	SkipServiceRegistration *bool             `json:"skip_service_registration,omitempty"`
}

// UpdateMachineRequest 对齐 openapi-generator 生成的结构体
type UpdateMachineRequest struct {
	Config                  *FlyMachineConfig `json:"config,omitempty"`
	CurrentVersion          *string           `json:"current_version,omitempty"`
	LeaseTtl                *int32            `json:"lease_ttl,omitempty"`
	Lsvd                    *bool             `json:"lsvd,omitempty"`
	MinSecretsVersion       *int32            `json:"min_secrets_version,omitempty"`
	Name                    *string           `json:"name,omitempty"`
	Region                  *string           `json:"region,omitempty"`
	SkipLaunch              *bool             `json:"skip_launch,omitempty"`
	SkipSecrets             *bool             `json:"skip_secrets,omitempty"`
	SkipServiceRegistration *bool             `json:"skip_service_registration,omitempty"`
}

// ListMachines 列出指定应用下的所有 Machine
// API文档: GET /apps/{app_name}/machines
func (s *MachineService) ListMachines(ctx context.Context, appName string) ([]*Machine, error) {
	if appName == "" {
		return nil, fmt.Errorf("app name cannot be empty for list machines")
	}
	logger.Info("Listing Fly.io machines", "AppName", appName)
	path := fmt.Sprintf("apps/%s/machines", appName)
	respBody, err := s.common.client.makeRequest(ctx, "GET", path, nil)
	if err != nil {
		logger.Error("Failed to list Fly.io machines", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to list machines: %w", err)
	}
	var machines []*Machine
	if err := json.Unmarshal(respBody, &machines); err != nil {
		logger.Error("Failed to unmarshal Fly.io machines response", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal list_machines response: %w", err)
	}
	logger.Info("Successfully listed Fly.io machines", "AppName", appName, "Count", len(machines))
	return machines, nil
}

// CreateMachine 创建一个新的 Machine
// API文档: POST /apps/{app_name}/machines
func (s *MachineService) CreateMachine(ctx context.Context, appName string, req *CreateMachineRequest) (*Machine, error) {
	if appName == "" {
		return nil, fmt.Errorf("app name cannot be empty for create machine")
	}
	logger.Info("Creating Fly.io machine", "AppName", appName)
	path := fmt.Sprintf("apps/%s/machines", appName)
	respBody, err := s.common.client.makeRequest(ctx, "POST", path, req)
	if err != nil {
		logger.Error("Failed to create Fly.io machine", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to create machine: %w", err)
	}
	var machine Machine
	if err := json.Unmarshal(respBody, &machine); err != nil {
		logger.Error("Failed to unmarshal Fly.io machine response", "AppName", appName, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal create_machine response: %w", err)
	}
	logger.Info("Successfully created Fly.io machine", "AppName", appName, "MachineID", machine.Id)
	return &machine, nil
}

// GetMachine 获取指定 Machine 详情
// API文档: GET /apps/{app_name}/machines/{machine_id}
func (s *MachineService) GetMachine(ctx context.Context, appName, machineID string) (*Machine, error) {
	if appName == "" || machineID == "" {
		return nil, fmt.Errorf("app name and machine id cannot be empty for get machine")
	}
	logger.Info("Getting Fly.io machine", "AppName", appName, "MachineID", machineID)
	path := fmt.Sprintf("apps/%s/machines/%s", appName, machineID)
	respBody, err := s.common.client.makeRequest(ctx, "GET", path, nil)
	if err != nil {
		logger.Error("Failed to get Fly.io machine", "AppName", appName, "MachineID", machineID, "Error", err)
		return nil, fmt.Errorf("failed to get machine: %w", err)
	}
	var machine Machine
	if err := json.Unmarshal(respBody, &machine); err != nil {
		logger.Error("Failed to unmarshal Fly.io machine response", "AppName", appName, "MachineID", machineID, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal get_machine response: %w", err)
	}
	logger.Info("Successfully got Fly.io machine", "AppName", appName, "MachineID", machine.Id)
	return &machine, nil
}

// UpdateMachine 更新指定 Machine
// API文档: POST /apps/{app_name}/machines/{machine_id}
func (s *MachineService) UpdateMachine(ctx context.Context, appName, machineID string, req *UpdateMachineRequest) (*Machine, error) {
	if appName == "" || machineID == "" {
		return nil, fmt.Errorf("app name and machine id cannot be empty for update machine")
	}
	logger.Info("Updating Fly.io machine", "AppName", appName, "MachineID", machineID)
	path := fmt.Sprintf("apps/%s/machines/%s", appName, machineID)
	respBody, err := s.common.client.makeRequest(ctx, "POST", path, req)
	if err != nil {
		logger.Error("Failed to update Fly.io machine", "AppName", appName, "MachineID", machineID, "Error", err)
		return nil, fmt.Errorf("failed to update machine: %w", err)
	}
	var machine Machine
	if err := json.Unmarshal(respBody, &machine); err != nil {
		logger.Error("Failed to unmarshal Fly.io machine response", "AppName", appName, "MachineID", machineID, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal update_machine response: %w", err)
	}
	logger.Info("Successfully updated Fly.io machine", "AppName", appName, "MachineID", machine.Id)
	return &machine, nil
}

// DeleteMachine 删除指定 Machine
// API文档: DELETE /apps/{app_name}/machines/{machine_id}?force=true
// force: 是否强制删除（kill 运行中的 machine）
func (s *MachineService) DeleteMachine(ctx context.Context, appName, machineID string, force bool) error {
	if appName == "" || machineID == "" {
		return fmt.Errorf("app name and machine id cannot be empty for delete machine")
	}
	logger.Info("Deleting Fly.io machine", "AppName", appName, "MachineID", machineID, "Force", force)
	path := fmt.Sprintf("apps/%s/machines/%s", appName, machineID)
	if force {
		path += "?force=true"
	}
	_, err := s.common.client.makeRequest(ctx, "DELETE", path, nil)
	if err != nil {
		logger.Error("Failed to delete Fly.io machine", "AppName", appName, "MachineID", machineID, "Force", force, "Error", err)
		return fmt.Errorf("failed to delete machine: %w", err)
	}
	logger.Info("Successfully deleted Fly.io machine", "AppName", appName, "MachineID", machineID, "Force", force)
	return nil
}

// MachineExecRequest 表示执行命令的请求结构
// 对齐 openapi-generator 生成的结构体
type MachineExecRequest struct {
	Cmd       *string  `json:"cmd,omitempty"`       // Deprecated: use Command instead
	Command   []string `json:"command,omitempty"`   // 要执行的命令数组
	Container *string  `json:"container,omitempty"` // 容器名称
	Stdin     *string  `json:"stdin,omitempty"`     // 标准输入
	Timeout   *int32   `json:"timeout,omitempty"`   // 超时时间（秒）
}

// MachineExecResponse 表示执行命令的响应结构
// 对齐 openapi-generator 生成的结构体
type MachineExecResponse struct {
	ExitCode   *int32  `json:"exit_code,omitempty"`   // 退出码
	ExitSignal *int32  `json:"exit_signal,omitempty"` // 退出信号
	Stderr     *string `json:"stderr,omitempty"`      // 标准错误输出
	Stdout     *string `json:"stdout,omitempty"`      // 标准输出
}

// StartMachine 启动指定 Machine
// API文档: POST /apps/{app_name}/machines/{machine_id}/start
func (s *MachineService) StartMachine(ctx context.Context, appName, machineID string) error {
	if appName == "" || machineID == "" {
		return fmt.Errorf("app name and machine id cannot be empty for start machine")
	}
	logger.Info("Starting Fly.io machine", "AppName", appName, "MachineID", machineID)
	path := fmt.Sprintf("apps/%s/machines/%s/start", appName, machineID)
	_, err := s.common.client.makeRequest(ctx, "POST", path, nil)
	if err != nil {
		logger.Error("Failed to start Fly.io machine", "AppName", appName, "MachineID", machineID, "Error", err)
		return fmt.Errorf("failed to start machine: %w", err)
	}
	logger.Info("Successfully started Fly.io machine", "AppName", appName, "MachineID", machineID)
	return nil
}

// ExecMachine 在指定 Machine 上执行命令
// API文档: POST /apps/{app_name}/machines/{machine_id}/exec
func (s *MachineService) ExecMachine(ctx context.Context, appName, machineID string, req *MachineExecRequest) (*MachineExecResponse, error) {
	if appName == "" || machineID == "" {
		return nil, fmt.Errorf("app name and machine id cannot be empty for exec machine")
	}
	if req == nil {
		return nil, fmt.Errorf("exec request cannot be nil")
	}
	logger.Info("Executing command on Fly.io machine", "AppName", appName, "MachineID", machineID)
	path := fmt.Sprintf("apps/%s/machines/%s/exec", appName, machineID)
	respBody, err := s.common.client.makeRequest(ctx, "POST", path, req)
	if err != nil {
		logger.Error("Failed to execute command on Fly.io machine", "AppName", appName, "MachineID", machineID, "Error", err)
		return nil, fmt.Errorf("failed to exec machine: %w", err)
	}
	var response MachineExecResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		logger.Error("Failed to unmarshal Fly.io machine exec response", "AppName", appName, "MachineID", machineID, "Error", err)
		return nil, fmt.Errorf("failed to unmarshal exec_machine response: %w", err)
	}
	logger.Info("Successfully executed command on Fly.io machine", "AppName", appName, "MachineID", machineID, "ExitCode", response.ExitCode)
	return &response, nil
}
