package agent_service_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/web-builder-dev/be-web-builder/internal/application/agent_service"
	"github.com/web-builder-dev/be-web-builder/internal/domain/agent/entity"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/retry"
)

// MockAgentRepository 是 AgentRepository 的模拟实现
type MockAgentRepository struct {
	mock.Mock
}

func (m *MockAgentRepository) Register(ctx context.Context, agent *entity.Agent) error {
	args := m.Called(ctx, agent)
	return args.Error(0)
}

func (m *MockAgentRepository) Unregister(ctx context.Context, agentID string) error {
	args := m.Called(ctx, agentID)
	return args.Error(0)
}

func (m *MockAgentRepository) GetByID(ctx context.Context, id string) (*entity.Agent, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.Agent), args.Error(1)
}

func (m *MockAgentRepository) UpdateLastActive(ctx context.Context, id string, lastActive int64) error {
	args := m.Called(ctx, id, lastActive)
	return args.Error(0)
}

func (m *MockAgentRepository) ListAgents(ctx context.Context) ([]*entity.Agent, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entity.Agent), args.Error(1)
}

func (m *MockAgentRepository) SendCommand(ctx context.Context, agentID string, cmd *entity.Command) error {
	args := m.Called(ctx, agentID, cmd)
	return args.Error(0)
}

func (m *MockAgentRepository) SendCommandAndWait(ctx context.Context, agentID string, cmd *entity.Command) ([]byte, error) {
	args := m.Called(ctx, agentID, cmd)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]byte), args.Error(1)
}

func TestAgentManagerService_IsAgentConnected(t *testing.T) {
	tests := []struct {
		name           string
		agentID        string
		mockAgent      *entity.Agent
		mockError      error
		expectedResult bool
	}{
		{
			name:    "agent connected",
			agentID: "test-agent-1",
			mockAgent: &entity.Agent{
				ID:        "test-agent-1",
				Connected: true,
			},
			mockError:      nil,
			expectedResult: true,
		},
		{
			name:    "agent not connected",
			agentID: "test-agent-2",
			mockAgent: &entity.Agent{
				ID:        "test-agent-2",
				Connected: false,
			},
			mockError:      nil,
			expectedResult: false,
		},
		{
			name:           "agent not found",
			agentID:        "non-existent-agent",
			mockAgent:      nil,
			mockError:      errors.New("agent not found"),
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock repository
			mockRepo := new(MockAgentRepository)
			mockRepo.On("GetByID", mock.Anything, tt.agentID).Return(tt.mockAgent, tt.mockError)

			// 创建service
			service := agent_service.NewAgentManagerService(mockRepo)

			// 执行测试
			ctx := context.Background()
			result := service.IsAgentConnected(ctx, tt.agentID)

			// 验证结果
			assert.Equal(t, tt.expectedResult, result)
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestAgentManagerService_WaitForAgentConnection_Success(t *testing.T) {
	// 创建mock repository
	mockRepo := new(MockAgentRepository)
	
	// 模拟第一次调用返回未连接，第二次调用返回已连接
	mockRepo.On("GetByID", mock.Anything, "test-agent").Return(&entity.Agent{
		ID:        "test-agent",
		Connected: false,
	}, nil).Once()
	
	mockRepo.On("GetByID", mock.Anything, "test-agent").Return(&entity.Agent{
		ID:        "test-agent",
		Connected: true,
	}, nil).Once()

	// 创建service
	service := agent_service.NewAgentManagerService(mockRepo)

	// 执行测试
	ctx := context.Background()
	timeout := 10 * time.Second
	
	start := time.Now()
	err := service.WaitForAgentConnection(ctx, "test-agent", timeout)
	duration := time.Since(start)

	// 验证结果
	require.NoError(t, err)
	assert.True(t, duration >= 2*time.Second, "Should wait at least one tick interval")
	assert.True(t, duration < timeout, "Should not timeout")
	mockRepo.AssertExpectations(t)
}

func TestAgentManagerService_WaitForAgentConnection_Timeout(t *testing.T) {
	// 创建mock repository
	mockRepo := new(MockAgentRepository)
	
	// 模拟始终返回未连接状态
	mockRepo.On("GetByID", mock.Anything, "test-agent").Return(&entity.Agent{
		ID:        "test-agent",
		Connected: false,
	}, nil)

	// 创建service
	service := agent_service.NewAgentManagerService(mockRepo)

	// 执行测试
	ctx := context.Background()
	timeout := 3 * time.Second // 使用较短的超时时间
	
	start := time.Now()
	err := service.WaitForAgentConnection(ctx, "test-agent", timeout)
	duration := time.Since(start)

	// 验证结果
	require.Error(t, err)
	assert.Contains(t, err.Error(), "timeout waiting for agent")
	assert.True(t, duration >= timeout, "Should timeout after specified duration")
	mockRepo.AssertExpectations(t)
}

func TestAgentManagerService_SendCommandToAgentWithRetry_Success(t *testing.T) {
	// 创建mock repository
	mockRepo := new(MockAgentRepository)
	
	// 模拟第一次失败，第二次成功
	mockRepo.On("SendCommand", mock.Anything, "test-agent", mock.AnythingOfType("*entity.Command")).Return(
		errors.New("agent with id test-agent not found for sending command")).Once()
	mockRepo.On("SendCommand", mock.Anything, "test-agent", mock.AnythingOfType("*entity.Command")).Return(nil).Once()

	// 创建service
	service := agent_service.NewAgentManagerService(mockRepo)

	// 创建重试配置
	retryConfig := &retry.RetryConfig{
		MaxAttempts:   3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      500 * time.Millisecond,
		BackoffFactor: 1.5,
		JitterFactor:  0.1,
		RetryableErrors: []string{
			"agent with id",
			"not found for sending command",
		},
	}

	// 执行测试
	ctx := context.Background()
	cmd := entity.NewUpdateCodeCommand()
	
	err := service.SendCommandToAgentWithRetry(ctx, "test-agent", &cmd, retryConfig)

	// 验证结果
	require.NoError(t, err)
	mockRepo.AssertExpectations(t)
}

func TestAgentManagerService_SendCommandToAgentWithRetry_MaxAttemptsReached(t *testing.T) {
	// 创建mock repository
	mockRepo := new(MockAgentRepository)
	
	// 模拟始终失败
	mockRepo.On("SendCommand", mock.Anything, "test-agent", mock.AnythingOfType("*entity.Command")).Return(
		errors.New("agent with id test-agent not found for sending command"))

	// 创建service
	service := agent_service.NewAgentManagerService(mockRepo)

	// 创建重试配置
	retryConfig := &retry.RetryConfig{
		MaxAttempts:   2,
		InitialDelay:  50 * time.Millisecond,
		MaxDelay:      100 * time.Millisecond,
		BackoffFactor: 1.5,
		JitterFactor:  0.1,
		RetryableErrors: []string{
			"agent with id",
			"not found for sending command",
		},
	}

	// 执行测试
	ctx := context.Background()
	cmd := entity.NewUpdateCodeCommand()
	
	err := service.SendCommandToAgentWithRetry(ctx, "test-agent", &cmd, retryConfig)

	// 验证结果
	require.Error(t, err)
	assert.Contains(t, err.Error(), "failed after 2 attempts")
	mockRepo.AssertExpectations(t)
}
