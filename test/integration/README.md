# 集成测试文档

## 概述

本目录包含了be-web-builder项目的集成测试，主要用于验证Agent连接等待和重试机制的有效性。

## 测试文件

### agent_connection_integration_test.go

这个文件包含了针对fly.io机器启动时序问题解决方案的集成测试。

#### 测试场景

1. **SlowMachineStartup_WaitForConnection_Success**
   - 测试机器启动慢但最终连接成功的情况
   - 验证连接等待机制的有效性

2. **SlowMachineStartup_WaitTimeout**
   - 测试机器启动太慢导致等待超时的情况
   - 验证超时处理逻辑

3. **NetworkIssue_RetryMechanism**
   - 测试网络问题导致命令发送失败的情况
   - 验证重试机制的有效性

4. **ConcurrentAgentConnections**
   - 测试多个Agent同时连接的情况
   - 验证并发处理能力

## 手动集成测试指南

由于集成测试需要真实的fly.io环境和WebSocket连接，以下是手动测试的步骤：

### 环境准备

1. 启动be-web-builder服务
2. 确保fly.io配置正确
3. 准备测试项目数据

### 测试场景1：正常机器启动

**步骤：**
1. 创建一个新项目
2. 观察日志中的Agent连接等待过程
3. 验证update_code命令成功发送

**预期结果：**
- 项目成功创建，无错误
- 日志显示Agent连接等待和成功连接
- update_code命令成功发送

**验证指标：**
- Agent连接等待时间 < 2分钟
- 命令发送成功率 = 100%
- 无连接超时错误

### 测试场景2：机器启动慢

**步骤：**
1. 在fly.io控制台中观察机器启动时间
2. 如果机器启动超过30秒，观察等待逻辑是否正常工作
3. 验证系统最终成功

**预期结果：**
- 系统等待Agent连接，最终成功
- 日志显示连接等待过程
- 无过早的超时错误

**验证指标：**
- 等待时间 >= 机器实际启动时间
- 最终成功率 = 100%（如果机器启动时间 < 5分钟）
- 日志包含"Still waiting for agent connection"消息

### 测试场景3：网络问题

**步骤：**
1. 启动be-web-builder服务和一个Agent
2. 在Agent连接后，模拟网络中断
3. 尝试发送update_code命令
4. 观察重试机制

**预期结果：**
- 系统自动重试
- 最终成功或给出明确错误
- 重试次数符合配置

**验证指标：**
- 重试次数 <= 5次
- 重试间隔符合指数退避策略
- 最终成功率 > 80%

### 测试场景4：并发连接

**步骤：**
1. 同时创建多个项目（如5个）
2. 观察并发Agent连接处理
3. 验证所有Agent都能正确连接

**预期结果：**
- 所有Agent都成功连接
- 系统稳定运行
- 无资源竞争问题

**验证指标：**
- 并发连接成功率 = 100%
- 平均连接时间 < 3分钟
- 内存和CPU使用正常

## 监控指标

### 连接等待时间
- **平均等待时间**：正常情况下应 < 30秒
- **最大等待时间**：不应超过5分钟
- **超时率**：应 < 5%

### 命令发送成功率
- **首次成功率**：应 > 90%
- **重试后成功率**：应 > 95%
- **最终失败率**：应 < 1%

### 错误分类
- **连接超时错误**：应包含明确的超时信息
- **网络错误**：应触发重试机制
- **Agent未找到错误**：应在连接建立前等待
- **其他错误**：应有详细的错误信息

### 性能指标
- **项目创建总时间**：应 < 5分钟
- **Agent启动时间**：通常 < 2分钟
- **命令响应时间**：应 < 10秒

## 日志检查要点

### 成功场景的日志
```
INFO Step 2: Waiting for agent connection ProjectID=xxx
INFO Agent connected successfully AgentID=xxx
INFO Step 2: Sending update_code command to agent with retry ProjectID=xxx
INFO Update code command sent successfully AgentID=xxx
```

### 等待超时的日志
```
INFO Step 2: Waiting for agent connection ProjectID=xxx
DEBUG Still waiting for agent connection AgentID=xxx
ERROR Failed to wait for agent connection xxx: timeout waiting for agent xxx to connect after 5m0s
```

### 重试成功的日志
```
WARN Retryable error encountered, retrying after delay error="agent with id xxx not found" attempt=1
INFO Request succeeded after retry attempt=3
```

## 故障排查

### 常见问题

1. **Agent连接超时**
   - 检查fly.io机器状态
   - 验证WebSocket连接配置
   - 检查网络连通性

2. **重试机制不生效**
   - 验证错误类型是否在RetryableErrors列表中
   - 检查重试配置参数
   - 查看详细的错误日志

3. **并发连接问题**
   - 检查资源限制
   - 验证连接池配置
   - 监控系统资源使用

### 调试建议

1. 启用DEBUG级别日志
2. 监控fly.io机器启动时间
3. 检查WebSocket连接状态
4. 分析错误模式和频率

## 运行测试

```bash
# 运行所有集成测试
cd test/integration
go test -v

# 运行特定测试
go test -v -run TestAgentConnectionIntegration_Documentation
```

注意：大部分集成测试场景需要在真实环境中手动执行，自动化测试主要用于文档化和基本验证。
