# Fly.io机器启动时序问题 - 实施计划

## 问题总结
当fly.io机器启动过慢时，WebSocket连接还未建立完成就发送update_code命令，导致"agent not found"错误。

## 解决方案概述
在发送update_code命令前添加Agent连接等待机制，并结合重试机制确保命令发送成功。

## 实施步骤

### 第一阶段：添加Agent连接检查和等待机制

#### 步骤1：修改AgentManagerService
**文件**: `internal/application/agent_service/agent_manager_service.go`

**需要添加的方法**:
1. `IsAgentConnected` - 检查agent是否已连接
2. `WaitForAgentConnection` - 等待agent连接建立，注意回收goroutine资源
3. `SendCommandToAgentWithRetry` - 使用重试机制发送命令

**预计工作量**: 2小时

#### 步骤2：修改CommitAndDeployService
**文件**: `internal/application/workflow_service/commit_and_deploy_service.go`

**修改内容**:
- 在发送update_code命令前添加连接等待逻辑
- 使用重试机制作为备用保障
- 添加详细的日志记录

**预计工作量**: 1小时

### 第二阶段：测试和验证

#### 步骤3：编写单元测试
**文件**: 
- `internal/application/agent_service/agent_manager_service_test.go`
- `internal/application/workflow_service/commit_and_deploy_service_test.go`

**测试内容**:
- Agent连接等待逻辑
- 重试机制
- 超时处理

**预计工作量**: 3小时

#### 步骤4：集成测试
**测试场景**:
- 机器启动慢的情况
- 网络异常情况
- 多项目并发创建

**预计工作量**: 2小时

### 第三阶段：部署和监控

#### 步骤5：配置优化
**配置项**:
- Agent连接等待超时时间（默认5分钟）
- 重试次数（默认5次）
- 重试间隔（2秒起始，最大10秒）

#### 步骤6：监控和告警
**监控指标**:
- Agent连接等待时间
- 命令发送成功率
- 重试次数统计

## 关键代码修改点

### 1. AgentManagerService新增方法

```go
// WaitForAgentConnection 等待agent连接建立
func (s *AgentManagerService) WaitForAgentConnection(ctx context.Context, agentID string, timeout time.Duration) error {
    // 实现等待逻辑
}

// SendCommandToAgentWithRetry 使用重试机制发送命令  
func (s *AgentManagerService) SendCommandToAgentWithRetry(ctx context.Context, agentID string, cmd *entity.Command, retryConfig *retry.RetryConfig) error {
    // 实现重试逻辑
}
```

### 2. CommitAndDeployService修改

```go
// 在发送update_code命令前添加等待
if err := s.agentManagerSvc.WaitForAgentConnection(ctx, agentID, 5*time.Minute); err != nil {
    return fmt.Errorf("failed to wait for agent connection: %w", err)
}

// 使用重试机制发送命令
cmd := agentEntity.NewUpdateCodeCommand()
if err := s.agentManagerSvc.SendCommandToAgentWithRetry(ctx, agentID, &cmd, retryConfig); err != nil {
    return fmt.Errorf("failed to send update_code command: %w", err)
}
```

## 风险评估

### 低风险
- ✅ 使用现有的retry包
- ✅ 不改变现有API接口
- ✅ 向后兼容

### 需要注意
- ⚠️ 超时时间设置需要合理（建议5分钟）
- ⚠️ 需要充分测试各种异常情况
- ⚠️ 添加详细日志便于问题排查

## 成功标准

1. **功能性**：update_code命令发送成功率达到99%以上
2. **性能**：Agent连接等待时间在合理范围内（通常30秒内）
3. **稳定性**：在机器启动慢的情况下不再出现"agent not found"错误
4. **可观测性**：有完整的日志和监控指标

## 回滚计划

如果出现问题，可以通过以下方式快速回滚：
1. 移除等待逻辑，恢复原有的直接发送命令方式
2. 通过配置开关控制是否启用新的等待机制
3. 调整超时时间和重试参数

## 总预计工作量
- **开发**: 6小时
- **测试**: 5小时  
- **部署和监控**: 2小时
- **总计**: 13小时（约2个工作日）

## 下一步行动

1. **立即开始**: 修改AgentManagerService添加连接等待方法
2. **优先级高**: 修改CommitAndDeployService的update_code发送逻辑
3. **并行进行**: 编写相关的单元测试
4. **最后完成**: 集成测试和部署监控

---

**请确认是否同意此实施计划，我将开始具体的代码修改工作。**
