package main

// @title Web Builder API
// @version 1.0
// @description This is the API for the Web Builder backend service.
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url https://www.web-builder-dev.com/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api
// @schemes http https

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/web-builder-dev/be-web-builder/internal/application"
	"github.com/web-builder-dev/be-web-builder/internal/application/agent_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/cloudflare_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/fly_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/github_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/netlify_service"
	"github.com/web-builder-dev/be-web-builder/internal/application/workflow_service"
	"github.com/web-builder-dev/be-web-builder/internal/config"
	infraCloudflare "github.com/web-builder-dev/be-web-builder/internal/infrastructure/cloudflare"
	"github.com/web-builder-dev/be-web-builder/internal/infrastructure/db/supabase"
	infraEdgeFunctions "github.com/web-builder-dev/be-web-builder/internal/infrastructure/edge_functions"
	infraFly "github.com/web-builder-dev/be-web-builder/internal/infrastructure/fly"
	infraGitHub "github.com/web-builder-dev/be-web-builder/internal/infrastructure/github"
	infraNetlify "github.com/web-builder-dev/be-web-builder/internal/infrastructure/netlify"
	infraAgent "github.com/web-builder-dev/be-web-builder/internal/infrastructure/websocket"
	"github.com/web-builder-dev/be-web-builder/internal/interface/api/rest"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/logger"
	"github.com/web-builder-dev/be-web-builder/internal/pkg/metrics"
)

const (
	defaultAppEnv           = "dev"
	defaultServerAddress    = ":8080"
	gracefulShutdownTimeout = 10 * time.Second
)

func main() {
	// 1. 加载环境和配置
	appEnv := os.Getenv("APP_ENV")
	if appEnv == "" {
		appEnv = defaultAppEnv
	}

	cfg, err := config.LoadConfig(appEnv)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration for env '%s': %v\n", appEnv, err)
		os.Exit(1)
	}

	// 2. 初始化 Logger
	logger.InitLogger(cfg.Log.Level, appEnv)

	logger.Info("Application starting up...")
	logger.Info("Configuration loaded successfully", "environment", appEnv, "logLevel", cfg.Log.Level)

	// 2.1. 启动指标报告
	metrics.StartMetricsReporting(5 * time.Minute) // 每5分钟报告一次指标
	logger.Info("Metrics reporting started", "interval", "5m")

	// 3. 初始化依赖
	// 3.1. 数据库和仓库
	supabaseClient, err := supabase.NewClient(cfg.Supabase.URL, cfg.Supabase.ServiceKey, nil)
	if err != nil {
		logger.Fatal("Failed to initialize Supabase client", "Error", err)
	}
	projectRepo := supabase.NewSupabaseProjectRepository(supabaseClient)
	projectStatusRepo := supabase.NewSupabaseProjectStatusRepository(supabaseClient)

	// 3.2. 基础设施服务
	githubInfraSvc := infraGitHub.NewService(cfg.GitHub.Token)
	netlifyInfraSvc, err := infraNetlify.NewService(cfg.Netlify.Token) // 假设字段名为 Token
	if err != nil {
		logger.Fatal("Failed to initialize Netlify infrastructure service", "Error", err)
	}
	edgeFuncService, err := infraEdgeFunctions.NewService(cfg.Supabase.URL, cfg.Supabase.EdgeFunctionsToken, nil)
	if err != nil {
		logger.Fatal("Failed to initialize Edge Functions infrastructure service", "Error", err)
	}
	flyInfraSvc, err := infraFly.NewService(cfg)
	if err != nil {
		logger.Fatal("Failed to initialize Fly infrastructure service", "Error", err)
	}
	cloudflareInfraSvc, err := infraCloudflare.NewService(cfg.Cloudflare.APIKey, cfg.Cloudflare.APIEmail, cfg.Cloudflare.AccountID)
	if err != nil {
		logger.Fatal("Failed to initialize Cloudflare infrastructure service", "Error", err)
	}

	// 3.3. 应用服务
	agentRepo := infraAgent.NewAgentRepositoryImpl()
	agentManager := agent_service.NewAgentManagerService(agentRepo)
	workerKVSvc := cloudflare_service.NewWorkerKVService(cloudflareInfraSvc, cfg)
	agentSetupSvc := agent_service.NewAgentSetupService(projectRepo, projectStatusRepo, workerKVSvc)
	flyAppMachineSvc := fly_service.NewFlyAppMachineService(flyInfraSvc, cfg, projectStatusRepo)
	codeCommitSvc := github_service.NewCodeCommitService(githubInfraSvc)
	appNetlifyHookService := netlify_service.NewHookService(netlifyInfraSvc, cfg)
	initNetlifySiteSvc := netlify_service.NewInitNetlifySiteService(netlifyInfraSvc, githubInfraSvc, appNetlifyHookService, projectRepo, cfg)
	fileContentSvc := github_service.NewFileContentService(githubInfraSvc, cfg)
	migrationProcessorSvc := workflow_service.NewMigrationProcessorService()
	contentReplacerSvc := workflow_service.NewGitHubContentReplacerService(fileContentSvc, githubInfraSvc, cfg, codeCommitSvc)
	supabaseMigrationSvc := workflow_service.NewSupabaseMigrationService(cfg, edgeFuncService, contentReplacerSvc)
	copyProjectSvc := workflow_service.NewCopyProjectService(projectRepo, githubInfraSvc, initNetlifySiteSvc, fileContentSvc, migrationProcessorSvc, supabaseMigrationSvc, contentReplacerSvc, flyAppMachineSvc, cfg)
	screenshotService := workflow_service.NewScreenshotService(cfg.BEScreenshotAPIBaseURL)
	netlifyHookProcessorService := workflow_service.NewNetlifyHookProcessorService(projectStatusRepo, netlifyInfraSvc, screenshotService)
	publishService := workflow_service.NewPublishService(netlifyInfraSvc, projectStatusRepo, projectRepo, agentManager, initNetlifySiteSvc, workerKVSvc, cfg)
	commitAndDeployService := workflow_service.NewCommitAndDeployService(projectRepo, projectStatusRepo, githubInfraSvc, codeCommitSvc, netlifyInfraSvc, initNetlifySiteSvc, agentManager, cfg, screenshotService)
	deleteProjectService := workflow_service.NewDeleteProjectService(projectRepo, projectStatusRepo, flyInfraSvc, netlifyInfraSvc)

	// 3.4. HTTP 处理器
	errorHandler := application.NewErrorHandler(projectStatusRepo)
	workflowHandler := rest.NewWorkflowHandler(copyProjectSvc, publishService, commitAndDeployService, deleteProjectService, errorHandler)
	netlifyHookHandler := rest.NewNetlifyHookHandler(netlifyHookProcessorService, errorHandler)
	cloudflareHookProcessorService := workflow_service.NewCloudflareHookProcessorService(projectStatusRepo)
	cloudflareHookHandler := rest.NewCloudflareHookHandler(cloudflareHookProcessorService, errorHandler, cfg.Cloudflare.WebhookAuthKey)
	fileContentHandler := rest.NewFileContentHandler(fileContentSvc)
	flyHandler := rest.NewFlyHandler(flyAppMachineSvc)
	// 4. 获取配置好的 Gin 路由器
	router := rest.NewRouter(logger.SugaredLogger(), appEnv, workflowHandler, netlifyHookHandler, cloudflareHookHandler, fileContentHandler, flyHandler, agentManager, agentSetupSvc, agentRepo)

	// 5. 设置和启动 HTTP 服务器
	serverAddress := os.Getenv("SERVER_ADDRESS")
	if serverAddress == "" {
		serverAddress = defaultServerAddress
	}

	srv := &http.Server{
		Addr:    serverAddress,
		Handler: router,
		// ReadTimeout:  10 * time.Second,
		// WriteTimeout: 10 * time.Second,
		// IdleTimeout:  15 * time.Second,
	}

	go func() {
		logger.Info("Starting server on ", "address", serverAddress)
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logger.Fatal("Failed to start server:", "Error", err)
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	receivedSignal := <-quit
	logger.Info("Shutdown signal received: ", "signal", receivedSignal.String())
	logger.Info("Server is shutting down gracefully...")

	ctx, cancel := context.WithTimeout(context.Background(), gracefulShutdownTimeout)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown:", "Error", err)
	}

	logger.Info("Server exited properly")
}
